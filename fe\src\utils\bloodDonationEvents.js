import { EventEmitter } from 'events';

// Create a global event emitter for blood donation events
const bloodDonationEvents = new EventEmitter();

// Event types for blood donation workflow
export const BLOOD_DONATION_EVENTS = {
  // Status and process updates
  STATUS_UPDATED: 'blood_donation_status_updated',
  PROCESS_UPDATED: 'blood_donation_process_updated',
  
  // Workflow stages
  DONATION_REGISTERED: 'blood_donation_registered',
  HEALTH_CHECK_COMPLETED: 'blood_donation_health_checked',
  BLOOD_DONATED: 'blood_donation_donated',
  BLOOD_TESTED: 'blood_donation_tested',
  DONATION_COMPLETED: 'blood_donation_completed',
  BLOOD_STORED: 'blood_donation_stored',
  
  // Rejection/cancellation events
  DONATION_REJECTED: 'blood_donation_rejected',
  DONATION_CANCELLED: 'blood_donation_cancelled',
  
  // Appointment events
  APPOINTMENT_CREATED: 'blood_donation_appointment_created',
  APPOINTMENT_UPDATED: 'blood_donation_appointment_updated',
  APPOINTMENT_DELETED: 'blood_donation_appointment_deleted',
  
  // Notification events
  NOTIFICATION_SENT: 'blood_donation_notification_sent',
  REMINDER_SENT: 'blood_donation_reminder_sent',
};

// Export the event emitter
export { bloodDonationEvents };

// Helper function to emit status update
export const emitBloodDonationStatusUpdate = (donationId, oldStatus, newStatus, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.STATUS_UPDATED, {
    donationId,
    oldStatus,
    newStatus,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit process update
export const emitBloodDonationProcessUpdate = (donationId, oldProcess, newProcess, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.PROCESS_UPDATED, {
    donationId,
    oldProcess,
    newProcess,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit donation registration
export const emitBloodDonationRegistered = (donationData) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.DONATION_REGISTERED, {
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit health check completion
export const emitHealthCheckCompleted = (donationId, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.HEALTH_CHECK_COMPLETED, {
    donationId,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit blood donation completion
export const emitBloodDonated = (donationId, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.BLOOD_DONATED, {
    donationId,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit blood testing completion
export const emitBloodTested = (donationId, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.BLOOD_TESTED, {
    donationId,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit donation completion
export const emitDonationCompleted = (donationId, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.DONATION_COMPLETED, {
    donationId,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit blood storage
export const emitBloodStored = (donationId, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.BLOOD_STORED, {
    donationId,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit donation rejection
export const emitDonationRejected = (donationId, reason = null, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.DONATION_REJECTED, {
    donationId,
    reason,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit donation cancellation
export const emitDonationCancelled = (donationId, reason = null, donationData = null) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.DONATION_CANCELLED, {
    donationId,
    reason,
    donationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit appointment creation
export const emitAppointmentCreated = (appointmentData) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.APPOINTMENT_CREATED, {
    appointmentData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit appointment update
export const emitAppointmentUpdated = (appointmentId, oldData, newData) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.APPOINTMENT_UPDATED, {
    appointmentId,
    oldData,
    newData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit appointment deletion
export const emitAppointmentDeleted = (appointmentId) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.APPOINTMENT_DELETED, {
    appointmentId,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit notification sent
export const emitNotificationSent = (notificationData) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.NOTIFICATION_SENT, {
    notificationData,
    timestamp: new Date().toISOString(),
  });
};

// Helper function to emit reminder sent
export const emitReminderSent = (reminderData) => {
  bloodDonationEvents.emit(BLOOD_DONATION_EVENTS.REMINDER_SENT, {
    reminderData,
    timestamp: new Date().toISOString(),
  });
};

// Utility function to listen to all blood donation events (for debugging)
export const logAllBloodDonationEvents = () => {
  Object.values(BLOOD_DONATION_EVENTS).forEach(eventType => {
    bloodDonationEvents.on(eventType, (data) => {
      console.log(`🩸 Blood Donation Event [${eventType}]:`, data);
    });
  });
};

// Utility function to remove all listeners (for cleanup)
export const removeAllBloodDonationListeners = () => {
  bloodDonationEvents.removeAllListeners();
};

export default bloodDonationEvents;
