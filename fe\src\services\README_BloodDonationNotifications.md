# Blood Donation Notification System

## Tổng quan

Hệ thống thông báo hiến máu đư<PERSON>c thiết kế để gửi thông báo tự động cho người dùng khi có thay đổi trạng thái trong quy trình hiến máu, tư<PERSON>ng tự như hệ thống thông báo yêu cầu máu (`bloodRequestNotificationService`).

## Cấu trúc Files

### 1. `bloodDonationNotificationService.js`
Service chính để tạo và gửi thông báo hiến máu.

### 2. `bloodDonationEvents.js`
Event system để theo dõi và phát sự kiện khi có thay đổi trong quy trình hiến máu.

## Quy trình Hiến máu và Thông báo

### Các trạng thái chính:

1. **REGISTERED** (`"registered"`) - <PERSON><PERSON><PERSON> <PERSON>ý thành công
2. **HEALTH_CHECKED** (`"health_checked"`) - Đ<PERSON> khám sàng lọc
3. **NOT_ELIGIBLE_HEALTH** (`"not_eligible_health"`) - Không đủ điều kiện (sau khám)
4. **DONATED** (`"donated"`) - Đã hiến máu
5. **BLOOD_TESTED** (`"blood_tested"`) - Xét nghiệm
6. **NOT_ELIGIBLE_TEST** (`"not_eligible_test"`) - Không đủ điều kiện (sau xét nghiệm)
7. **COMPLETED** (`"completed"`) - Hoàn thành
8. **STORED** (`"stored"`) - Đã nhập kho

### Thông báo tương ứng:

- **REGISTERED**: "Đăng ký hiến máu của bạn đã được ghi nhận"
- **HEALTH_CHECKED**: "Bạn đã hoàn thành khám sàng lọc sức khỏe"
- **NOT_ELIGIBLE_HEALTH**: "Rất tiếc, bạn chưa đủ điều kiện hiến máu sau khám sức khỏe"
- **DONATED**: "Cảm ơn bạn đã hiến máu thành công! 🩸"
- **BLOOD_TESTED**: "Mẫu máu của bạn đã được xét nghiệm"
- **NOT_ELIGIBLE_TEST**: "Rất tiếc, mẫu máu của bạn không đạt yêu cầu sau xét nghiệm"
- **COMPLETED**: "Quy trình hiến máu của bạn đã hoàn thành! Cảm ơn bạn đã đóng góp 🙏"
- **STORED**: "Máu hiến tặng của bạn đã được lưu trữ an toàn trong kho máu"

## Cách sử dụng

### 1. Gửi thông báo khi cập nhật trạng thái

```javascript
import bloodDonationNotificationService from '../services/bloodDonationNotificationService';

// Gửi thông báo khi cập nhật trạng thái
await bloodDonationNotificationService.createBloodDonationNotification({
  userId: donorUserId,
  donationId: donationId,
  status: newStatus, // Sử dụng các constant từ DONATION_STATUS
  updatedBy: "Bác sĩ khoa Huyết học",
});
```

### 2. Gửi thông báo khi đăng ký hiến máu

```javascript
// Gửi thông báo khi đăng ký thành công
await bloodDonationNotificationService.createBloodDonationSubmissionNotification({
  userId: userId,
  donationId: donationId,
});
```

### 3. Gửi thông báo nhắc nhở lịch hẹn

```javascript
// Gửi thông báo nhắc nhở
await bloodDonationNotificationService.createAppointmentReminderNotification({
  userId: userId,
  donationId: donationId,
  appointmentDate: "2024-01-15",
  location: "Bệnh viện Ánh Dương",
});
```

## Tích hợp trong các Components

### 1. Doctor Actions (`useDoctorDonorActions.js`)
- Gửi thông báo khi bác sĩ cập nhật trạng thái hoặc process
- Hỗ trợ cả numeric status (0,1,2,3) và string status

### 2. Manager Actions (`EligibleDonorsPage.jsx`)
- Gửi thông báo khi quản lý cập nhật trạng thái
- Đặc biệt khi nhập kho máu (STORED status)

### 3. Appointment Creation (`useAppointmentScheduling.js`)
- Gửi thông báo khi tạo lịch hẹn thành công
- Tích hợp với reminder system

### 4. Donation Management (`DonationProcessManagement.jsx`)
- Gửi thông báo khi thêm đăng ký hiến máu mới

## Event System

### Các events được hỗ trợ:

```javascript
import { 
  emitBloodDonationStatusUpdate,
  emitBloodDonationProcessUpdate,
  emitAppointmentCreated,
  emitBloodDonationRegistered 
} from '../utils/bloodDonationEvents';

// Emit khi cập nhật status
emitBloodDonationStatusUpdate(donationId, oldStatus, newStatus, donationData);

// Emit khi cập nhật process
emitBloodDonationProcessUpdate(donationId, oldProcess, newProcess, donationData);

// Emit khi tạo appointment
emitAppointmentCreated(appointmentData);

// Emit khi đăng ký hiến máu
emitBloodDonationRegistered(donationData);
```

## Error Handling

Tất cả các notification calls đều được wrap trong try-catch để không làm gián đoạn workflow chính:

```javascript
try {
  await bloodDonationNotificationService.createBloodDonationNotification({
    userId: userId,
    donationId: donationId,
    status: status,
    updatedBy: updatedBy,
  });
} catch (notificationError) {
  console.warn("Could not send notification:", notificationError);
  // Không throw error - tiếp tục workflow chính
}
```

## API Format

Service hỗ trợ nhiều format dữ liệu để tương thích với backend:

1. **PascalCase** (C# style): `UserId`, `Title`, `Message`
2. **camelCase** (JavaScript style): `userId`, `title`, `message`
3. **Mixed case**: Kết hợp cả hai

## Testing

```javascript
// Test notification creation
const result = await bloodDonationNotificationService.testNotificationCreation(userId);
console.log('Test result:', result);
```

## Lưu ý

1. **Không làm gián đoạn workflow**: Tất cả notification calls đều non-blocking
2. **Backward compatibility**: Hỗ trợ cả numeric và string status
3. **Flexible API format**: Tự động thử nhiều format dữ liệu
4. **Event-driven**: Tích hợp với event system để tracking
5. **Error resilient**: Graceful handling khi notification API fails

## Tương lai

Có thể mở rộng thêm:
- Push notifications
- Email notifications
- SMS notifications
- Real-time updates qua WebSocket
- Notification preferences cho user
