// Test script để kiểm tra chức năng "<PERSON><PERSON><PERSON> dấu tất cả đã đọc"
// Chạy script này trong browser console để test

console.log("🧪 Testing Mark All As Read functionality");

// Mock data for testing
const mockUnreadNotifications = [
  { id: 1, title: "Test notification 1", isRead: false },
  { id: 2, title: "Test notification 2", isRead: false },
  { id: 3, title: "Test notification 3", isRead: false }
];

// Test function
async function testMarkAllAsRead() {
  console.log("📝 Test data:", mockUnreadNotifications);
  
  // Simulate the markAllAsRead function logic
  console.log("🔄 Starting mark all as read process...");
  
  const results = [];
  const errors = [];
  
  // Simulate API calls
  for (const notification of mockUnreadNotifications) {
    try {
      console.log(`📖 Marking notification ${notification.id} as read...`);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Simulate success (you can change this to test error scenarios)
      const success = Math.random() > 0.2; // 80% success rate
      
      if (success) {
        results.push({ id: notification.id, success: true });
        console.log(`✅ Notification ${notification.id} marked as read`);
      } else {
        throw new Error(`Failed to mark notification ${notification.id}`);
      }
      
    } catch (error) {
      errors.push({ id: notification.id, error: error.message });
      console.error(`❌ Error marking notification ${notification.id}:`, error.message);
    }
  }
  
  // Results summary
  const successCount = results.length;
  const errorCount = errors.length;
  
  console.log(`📊 Results: ${successCount} success, ${errorCount} errors`);
  
  if (successCount > 0) {
    const message = `${successCount} thông báo đã được đánh dấu đã đọc${errorCount > 0 ? `, ${errorCount} thông báo gặp lỗi` : ''}`;
    console.log(`✅ Success: ${message}`);
    return { success: true, message, results, errors };
  } else {
    const errorMessage = `Không thể đánh dấu thông báo nào. Lỗi: ${errors.map(e => e.error).join(', ')}`;
    console.log(`❌ Failed: ${errorMessage}`);
    throw new Error(errorMessage);
  }
}

// Run test
testMarkAllAsRead()
  .then(result => {
    console.log("🎉 Test completed successfully:", result);
  })
  .catch(error => {
    console.error("💥 Test failed:", error.message);
  });
