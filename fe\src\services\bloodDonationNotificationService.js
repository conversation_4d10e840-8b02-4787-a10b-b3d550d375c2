import axiosInstance from "./axiosInstance";
import config from "../config/environment";
import { DONATION_STATUS } from "../constants/systemConstants";

const NOTIFICATION_API = config.api.notification;

/**
 * Blood Donation Notification Service
 * Handles notifications specifically for blood donation status changes
 */
export const bloodDonationNotificationService = {
  
  
  createBloodDonationNotification: async ({ userId, donationId, status, updatedBy }) => {
    // Validate input parameters
    if (!userId || !donationId) {
      console.warn("🩸 Missing required parameters:", { userId, donationId, status, updatedBy });
      return {
        success: false,
        error: "Missing required parameters: userId or donationId",
      };
    }
    const statusMessages = {
      // String-based statuses
      [DONATION_STATUS.REGISTERED]: "Đăng ký hiến máu của bạn đã được ghi nhận",
      [DONATION_STATUS.HEALTH_CHECKED]: "Bạn đã hoàn thành khám sàng lọc sức khỏe",
      [DONATION_STATUS.NOT_ELIGIBLE_HEALTH]: "R<PERSON>t tiếc, bạn chưa đủ điều kiện hiến máu sau khám sức khỏe",
      [DONATION_STATUS.DONATED]: "Cảm ơn bạn đã hiến máu thành công! 🩸",
      [DONATION_STATUS.BLOOD_TESTED]: "Mẫu máu của bạn đã được xét nghiệm",
      [DONATION_STATUS.NOT_ELIGIBLE_TEST]: "Rất tiếc, mẫu máu của bạn không đạt yêu cầu sau xét nghiệm",
      [DONATION_STATUS.COMPLETED]: "Quy trình hiến máu của bạn đã hoàn thành! Cảm ơn bạn đã đóng góp 🙏",
      [DONATION_STATUS.STORED]: "Máu hiến tặng của bạn đã được lưu trữ an toàn trong kho máu",
      
      // Numeric statuses (for backward compatibility)
      0: "Đăng ký hiến máu của bạn đã được ghi nhận",
      1: "Bạn đã hoàn thành khám sàng lọc sức khỏe",
      2: "Cảm ơn bạn đã hiến máu thành công! 🩸",
      3: "Quy trình hiến máu của bạn đã hoàn thành! Cảm ơn bạn đã đóng góp 🙏",
      4: "Máu hiến tặng của bạn đã được lưu trữ an toàn trong kho máu",
    };

    // Try different data formats to match API expectations
    const notificationDataFormats = [
      // Format 1: PascalCase (C# style)
      {
        UserId: parseInt(userId),
        Title: "Cập nhật hiến máu",
        Message: statusMessages[status] || "Trạng thái hiến máu đã được cập nhật",
        Type: "blood_donation_update",
        RelatedId: parseInt(donationId),
        IsRead: false,
        CreatedBy: updatedBy,
        SentAt: new Date().toISOString(),
      },
      // Format 2: camelCase (JavaScript style)
      {
        userId: parseInt(userId),
        title: "Cập nhật hiến máu",
        message: statusMessages[status] || "Trạng thái hiến máu đã được cập nhật",
        type: "blood_donation_update",
        relatedId: parseInt(donationId),
        isRead: false,
        createdBy: updatedBy,
        sentAt: new Date().toISOString(),
      },
      // Format 3: Mixed case (common in APIs)
      {
        userId: parseInt(userId),
        Title: "Cập nhật hiến máu",
        Message: statusMessages[status] || "Trạng thái hiến máu đã được cập nhật",
        Type: "blood_donation_update",
        RelatedId: parseInt(donationId),
        IsRead: false,
        CreatedBy: updatedBy || "system",
        SentAt: new Date().toISOString(),
      },
      // Format 2: camelCase (JavaScript style)
      {
        userId: parseInt(userId),
        title: "Cập nhật hiến máu",
        message: statusMessages[status] || "Trạng thái hiến máu đã được cập nhật",
        type: "blood_donation_update",
        relatedId: parseInt(donationId),
        isRead: false,
        createdBy: updatedBy || "system",
        sentAt: new Date().toISOString(),
      },
      // Format 3: Mixed case (common in APIs)
      {
        userId: parseInt(userId),
        Title: "Cập nhật hiến máu",
        Message: statusMessages[status] || "Trạng thái hiến máu đã được cập nhật",
        Type: "blood_donation_update",
        RelatedId: parseInt(donationId),
        IsRead: false,
        CreatedBy: updatedBy || "system",
        SentAt: new Date().toISOString(),
      }
    ];

    // Try each format until one works
    for (let i = 0; i < notificationDataFormats.length; i++) {
      const notificationData = notificationDataFormats[i];

      // console.log(`🩸 Trying notification format ${i + 1}:`, notificationData);

      try {
        const response = await axiosInstance.post(NOTIFICATION_API, notificationData);
        console.log(`✅ Notification created successfully with format ${i + 1}:`, response.data);

        return {
          success: true,
          data: response.data,
          message: "Thông báo hiến máu đã được tạo thành công",
        };
      } catch (error) {
        // console.log(`❌ Format ${i + 1} failed:`, error.response?.status, error.response?.data);

        // If this is a 500 error but contains notification data, consider it success
        if (error.response?.status === 500) {
          const responseData = error.response?.data;

          // Check if response contains notification data (even if truncated)
          if (responseData && (
            responseData.notificationId ||
            responseData.userId ||
            (typeof responseData === 'string' && responseData.includes('notificationId'))
          )) {
            console.log(`✅ Notification created successfully despite 500 error:`, responseData);
            return {
              success: true,
              data: responseData,
              message: "Thông báo hiến máu đã được tạo thành công",
            };
          }
        }

        // If not the last format, continue to next format
        if (i < notificationDataFormats.length - 1) {
          // console.log(`⏭️ Trying next format...`);
          continue;
        }

        // If this is the last format and still failing, return error
        // console.log(`❌ All formats failed. Final error:`, error.response?.data);
        return {
          success: false,
          error: error.response?.data?.message || error.message || "Không thể tạo thông báo hiến máu",
          details: error.response?.data,
          status: error.response?.status,
        };
      }
    }
  },

  /**
   * Create notification for blood donation submission
   * @param {Object} params - Notification parameters
   * @param {number} params.userId - Target user ID
   * @param {number} params.donationId - Blood donation ID
   * @returns {Promise<Object>} Created notification
   */
  createBloodDonationSubmissionNotification: async ({ userId, donationId }) => {
    const notificationData = {
      userId: parseInt(userId),
      title: "Đăng ký hiến máu thành công",
      message: "Đăng ký hiến máu của bạn đã được gửi thành công. Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.",
      type: "blood_donation_submission",
      relatedId: parseInt(donationId),
      isRead: false,
      createdBy: "system",
      sentAt: new Date().toISOString(),
    };

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, notificationData);
      
      return {
        success: true,
        data: response.data,
        message: "Thông báo đăng ký hiến máu đã được tạo thành công",
      };
    } catch (error) {
      if (error.response?.status === 500 && error.response?.data) {
        if (error.response.data.notificationId || error.response.data.userId) {
          return {
            success: true,
            data: error.response.data,
            message: "Thông báo đăng ký hiến máu đã được tạo thành công",
          };
        }
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || "Không thể tạo thông báo đăng ký hiến máu",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Create notification for appointment reminder
   * @param {Object} params - Notification parameters
   * @param {number} params.userId - Target user ID
   * @param {number} params.donationId - Blood donation ID
   * @param {string} params.appointmentDate - Appointment date
   * @param {string} params.location - Appointment location
   * @returns {Promise<Object>} Created notification
   */
  createAppointmentReminderNotification: async ({ userId, donationId, appointmentDate, location }) => {
    const notificationData = {
      userId: parseInt(userId),
      title: "Nhắc nhở lịch hẹn hiến máu",
      message: `Bạn có lịch hẹn hiến máu vào ${appointmentDate}${location ? ` tại ${location}` : ''}. Vui lòng đến đúng giờ.`,
      type: "donation_appointment_reminder",
      relatedId: parseInt(donationId),
      isRead: false,
      createdBy: "system",
      sentAt: new Date().toISOString(),
    };

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, notificationData);
      
      return {
        success: true,
        data: response.data,
        message: "Thông báo nhắc nhở lịch hẹn đã được tạo thành công",
      };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || "Không thể tạo thông báo nhắc nhở",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Test notification creation
   * @param {number} userId - Target user ID
   * @returns {Promise<Object>} Test result
   */
  testNotificationCreation: async (userId) => {
    const testData = {
      userId: parseInt(userId),
      title: "Test Blood Donation Notification",
      message: "This is a test notification for blood donation",
      type: "test_blood_donation",
      isRead: false,
      createdBy: "test",
      sentAt: new Date().toISOString(),
    };

    try {
      const response = await axiosInstance.post(NOTIFICATION_API, testData);
      
      return { success: true, data: response.data };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data || error.message,
        status: error.response?.status 
      };
    }
  },

  /**
   * Test notification creation (for debugging)
   * @param {number} userId - Test user ID
   * @returns {Promise<Object>} Test result
   */
  testNotificationCreation: async (userId = 1) => {
    console.log("🩸 Testing blood donation notification creation...");

    const testResult = await bloodDonationNotificationService.createBloodDonationNotification({
      userId: userId,
      donationId: 999,
      status: DONATION_STATUS.REGISTERED,
      updatedBy: "Test System",
    });

    console.log("🩸 Test result:", testResult);
    return testResult;
  },
};

export default bloodDonationNotificationService;
