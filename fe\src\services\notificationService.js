import config from "../config/environment";

class NotificationService {
  constructor() {
    this.baseURL = config.api.baseUrl;
  }

  // GET /api/Notification/user/{userId}
  static async getNotifications(userId) {
    try {
      console.log(
        "🔄 NotificationService: Fetching notifications for userId:",
        userId
      );

      const response = await fetch(
        `${config.api.baseUrl}/Notification/user/${userId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("✅ NotificationService: API response:", data);

      // Sort notifications by sent date (newest first)
      const sortedData = data.sort((a, b) => {
        const dateA = new Date(a.SentAt || a.sentAt || a.createdAt);
        const dateB = new Date(b.SentAt || b.sentAt || b.createdAt);
        return dateB - dateA;
      });

      return sortedData;
    } catch (error) {
      console.error("❌ NotificationService: API error:", error);
      throw error; // Throw error instead of using mock data
    }
  }

  // GET /api/Notification/{id}
  static async getNotificationById(notificationId) {
    try {
      const response = await fetch(
        `${config.api.baseUrl}/Notification/${notificationId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error getting notification by ID:", error);
      return null;
    }
  }

  // GET /api/Notification/unread-count/{userId}
  static async getUnreadCount(userId) {
    try {
      const response = await fetch(
        `${config.api.baseUrl}/Notification/unread-count/${userId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const count = await response.json();
      return count;
    } catch (error) {
      console.error(
        "❌ NotificationService: Error getting unread count:",
        error
      );
      return 0; // Return 0 if API fails
    }
  }

  // PUT /api/Notification/mark-read/{id}
  static async markAsRead(notificationId) {
    try {
      console.log(
        "📖 NotificationService: Attempting to mark as read notification ID:",
        notificationId
      );

      const response = await fetch(
        `${config.api.baseUrl}/Notification/mark-read/${notificationId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if response has content before parsing JSON
      const text = await response.text();
      if (text) {
        try {
          const data = JSON.parse(text);
          console.log("✅ NotificationService: API mark-as-read successful");
          return data;
        } catch (jsonError) {
          console.log(
            "✅ NotificationService: API mark-as-read successful (no JSON response)"
          );
          return { success: true, message: "Notification marked as read" };
        }
      } else {
        // Empty response, assume success
        console.log(
          "✅ NotificationService: API mark-as-read successful (empty response)"
        );
        return { success: true, message: "Notification marked as read" };
      }
    } catch (error) {
      console.error(
        "❌ NotificationService: Error marking notification as read:",
        error
      );
      throw error; // Throw error instead of using mock fallback
    }
  }

  // Mark all notifications as read by calling mark-read API for each unread notification
  static async markAllAsRead(userId, unreadNotifications = []) {
    try {
      console.log(
        "📖 NotificationService: Marking all notifications as read for user:",
        userId,
        "Unread notifications count:",
        unreadNotifications.length
      );

      if (!unreadNotifications || unreadNotifications.length === 0) {
        console.log("📖 NotificationService: No unread notifications to mark");
        return { success: true, message: "No unread notifications to mark" };
      }

      // Create promises for all mark-as-read API calls to run them in parallel
      const markAsReadPromises = unreadNotifications.map(async (notification) => {
        const notificationId = notification.id ||
                              notification.notificationId ||
                              notification.NotificationId;

        if (!notificationId) {
          console.warn("📖 NotificationService: Skipping notification without ID:", notification);
          return { id: null, success: false, error: "No notification ID" };
        }

        try {
          console.log("📖 NotificationService: Marking notification as read:", notificationId);
          const result = await this.markAsRead(notificationId);
          return { id: notificationId, success: true, result };
        } catch (error) {
          console.error(
            "📖 NotificationService: Error marking notification as read:",
            notificationId,
            error
          );
          return { id: notificationId, success: false, error: error.message };
        }
      });

      // Wait for all API calls to complete
      const allResults = await Promise.all(markAsReadPromises);

      // Separate successful results from errors
      const results = allResults.filter(r => r.success && r.id);
      const errors = allResults.filter(r => !r.success && r.id);

      // Return success if at least some notifications were marked as read
      const successCount = results.length;
      const errorCount = errors.length;

      console.log(
        `📖 NotificationService: Mark all as read completed. Success: ${successCount}, Errors: ${errorCount}`
      );

      if (successCount > 0) {
        return {
          success: true,
          message: `${successCount} thông báo đã được đánh dấu đã đọc${errorCount > 0 ? `, ${errorCount} thông báo gặp lỗi` : ''}`,
          results,
          errors
        };
      } else {
        throw new Error(`Không thể đánh dấu thông báo nào. Lỗi: ${errors.map(e => e.error).join(', ')}`);
      }

    } catch (error) {
      console.error(
        "❌ NotificationService: Error in markAllAsRead:",
        error
      );
      throw error;
    }
  }

  // DELETE /api/Notification/{id}
  static async deleteNotification(notificationId) {
    try {
      console.log(
        "NotificationService: Attempting to delete notification ID:",
        notificationId
      );

      const response = await fetch(
        `${config.api.baseUrl}/Notification/${notificationId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("authToken")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if response has content before parsing JSON
      const text = await response.text();
      if (text) {
        try {
          const data = JSON.parse(text);
          console.log("✅ NotificationService: API delete successful");
          return data;
        } catch (jsonError) {
          console.log(
            "✅ NotificationService: API delete successful (no JSON response)"
          );
          return {
            success: true,
            message: "Notification deleted successfully",
          };
        }
      } else {
        // Empty response, assume success
        console.log(
          "✅ NotificationService: API delete successful (empty response)"
        );
        return { success: true, message: "Notification deleted successfully" };
      }
    } catch (error) {
      console.error(
        "❌ NotificationService: Error deleting notification:",
        error
      );
      throw error; // Throw error instead of using mock fallback
    }
  }

  // POST /api/Notification - Create notification
  static async createNotification(notification) {
    try {
      // Debug logging
      console.log("Creating notification for:", notification);

      // Match database schema: NotificationID, UserID, Title, Message, IsRead, SENT
      const payload = {
        UserID: parseInt(notification.userId),
        Title: notification.title,
        Message: notification.message,
        IsRead: false,
        SENT: new Date().toISOString(),
      };

      console.log("Notification payload:", payload);

      const response = await fetch(`${config.api.baseUrl}/Notification`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("authToken")}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        // Log response details for debugging
        const errorText = await response.text();
        console.error("Notification API error response:", {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
        });
        throw new Error(
          `HTTP error! status: ${response.status} - ${errorText}`
        );
      }

      const data = await response.json();
      console.log("Notification created successfully:", data);
      return data;
    } catch (error) {
      console.error("Error creating notification:", error);

      // Fallback to mock data if API fails
      const newNotification = {
        id: Date.now(),
        ...notification,
        isRead: false,
        createdAt: new Date().toISOString(),
      };
      console.log("Created notification in fallback:", newNotification);
      return newNotification;
    }
  }

  // Calculate next eligible donation date
  static calculateNextEligibleDate(lastDonationDate, gender = "male") {
    const lastDate = new Date(lastDonationDate);
    const waitingPeriod = gender === "female" ? 84 : 56; // days
    const nextDate = new Date(lastDate);
    nextDate.setDate(nextDate.getDate() + waitingPeriod);
    return nextDate;
  }

  // Check if user is eligible to donate
  static isEligibleToDonate(lastDonationDate, gender = "male") {
    if (!lastDonationDate) return true;

    const nextEligibleDate = this.calculateNextEligibleDate(
      lastDonationDate,
      gender
    );
    return new Date() >= nextEligibleDate;
  }

  // Get days until next eligible donation
  static getDaysUntilEligible(lastDonationDate, gender = "male") {
    if (!lastDonationDate) return 0;

    const nextEligibleDate = this.calculateNextEligibleDate(
      lastDonationDate,
      gender
    );
    const today = new Date();
    const diffTime = nextEligibleDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  // Send donation reminder notification
  static async sendDonationReminder(userId, userData) {
    const daysUntilEligible = this.getDaysUntilEligible(
      userData.lastDonationDate,
      userData.gender
    );

    if (daysUntilEligible <= 3 && daysUntilEligible >= 0) {
      const message =
        daysUntilEligible === 0
          ? "Bạn đã có thể hiến máu trở lại! Hãy đăng ký lịch hẹn ngay."
          : `Còn ${daysUntilEligible} ngày nữa bạn có thể hiến máu trở lại.`;

      return this.createNotification({
        userId,
        type: "Reminder", // Database enum value
        title: "🩸 Nhắc nhở hiến máu",
        message,
        priority: false, // Normal priority
      });
    }
    return null;
  }

  // Send urgent blood request notification
  static async sendUrgentBloodRequest(userId, requestData) {
    return this.createNotification({
      userId,
      type: "Alert", // Database enum value for urgent requests
      title: "🚨 Yêu cầu máu khẩn cấp",
      message: `Cần gấp máu nhóm ${requestData.bloodType}. Bạn có thể giúp đỡ không?`,
      priority: true, // High priority for urgent requests
    });
  }

  // Send appointment reminder notification
  static async sendAppointmentReminder(userId, appointmentData) {
    const appointmentDate = new Date(appointmentData.appointmentDate);
    const formattedDate = appointmentDate.toLocaleDateString("vi-VN");
    const formattedTime = appointmentDate.toLocaleTimeString("vi-VN", {
      hour: "2-digit",
      minute: "2-digit",
    });

    return this.createNotification({
      userId,
      type: "Reminder", // Database enum value
      title: "Nhắc nhở lịch hẹn",
      message: `Bạn có lịch hẹn hiến máu vào ${formattedDate} lúc ${formattedTime}.`,
      priority: false, // Normal priority for appointment reminders
    });
  }

  // Blacklisted notification types that should not be displayed
  static BLACKLISTED_TYPES = [
    "delete_notification",
    "notification_deleted",
    "notification_delete",
    "delete_success",
    "removed_notification",
  ];

  // Blacklisted keywords in title/message
  static BLACKLISTED_KEYWORDS = [
    "delete notification",
    "xóa thông báo",
    "notification deleted",
    "thông báo đã xóa",
    "removed notification",
    "đã xóa thông báo",
  ];

  // Filter out deleted notifications and unwanted notification messages
  static filterActiveNotifications(notifications) {
    return notifications.filter((notification) => {
      // Filter out soft deleted notifications
      const isDeleted =
        notification.isDeleted ||
        notification.IsDeleted ||
        notification.isDeleted === true ||
        notification.IsDeleted === true;

      // Filter out blacklisted notification types
      const notificationType = (
        notification.type ||
        notification.Type ||
        ""
      ).toLowerCase();
      const isBlacklistedType =
        this.BLACKLISTED_TYPES.includes(notificationType);

      // Filter out notifications with blacklisted keywords
      const hasBlacklistedKeywords = this.BLACKLISTED_KEYWORDS.some(
        (keyword) =>
          notification.message?.toLowerCase().includes(keyword) ||
          notification.title?.toLowerCase().includes(keyword)
      );

      return !isDeleted && !isBlacklistedType && !hasBlacklistedKeywords;
    });
  }

  // Format notification time
  static formatNotificationTime(createdAt) {
    if (!createdAt) return "Chưa có thông tin";

    const now = new Date();
    const notificationTime = new Date(createdAt);

    // Check if date is valid
    if (isNaN(notificationTime.getTime())) {
      return "Chưa có thông tin";
    }

    const diffMs = now - notificationTime;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Vừa xong";
    if (diffMins < 60) return `${diffMins} phút trước`;
    if (diffHours < 24) return `${diffHours} giờ trước`;
    if (diffDays < 7) return `${diffDays} ngày trước`;

    return notificationTime.toLocaleDateString("vi-VN");
  }

  // Get notification icon based on type
  static getNotificationIcon(type) {
    // Use dynamic import to avoid circular dependency
    import('../constants/iconConstants').then(({ getNotificationIcon }) => {
      return getNotificationIcon(type);
    });

    // Fallback for immediate return
    const icons = {
      // New database types
      Reminder: "R",
      Alert: "!",
      Report: "R",
      // Legacy types
      donation_reminder: "D",
      urgent_request: "!",
      appointment_reminder: "R",
      health_check: "H",
      donation_thanks: "T",
      blood_request_update: "U",
      system_announcement: "A",
      account_update: "U",
    };
    return icons[type] || "N";
  }

  // Get notification color based on type
  static getNotificationColor(type) {
    const colors = {
      // New database types
      Reminder: "#17a2b8",
      Alert: "#dc3545",
      Report: "#6f42c1",
      // Legacy types
      donation_reminder: "#28a745",
      urgent_request: "#dc3545",
      appointment_reminder: "#17a2b8",
      health_check: "#6f42c1",
      donation_thanks: "#e83e8c",
      blood_request_update: "#ffc107",
      system_announcement: "#6c757d",
      account_update: "#3b82f6",
    };
    return colors[type] || "#6c757d";
  }

  // Event broadcasting for real-time notifications
  static notificationEventListeners = new Map();

  static addEventListener(eventType, callback) {
    if (!this.notificationEventListeners.has(eventType)) {
      this.notificationEventListeners.set(eventType, []);
    }
    this.notificationEventListeners.get(eventType).push(callback);
  }

  static removeEventListener(eventType, callback) {
    if (this.notificationEventListeners.has(eventType)) {
      const listeners = this.notificationEventListeners.get(eventType);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  static broadcastNotificationEvent(eventType, data) {
    if (this.notificationEventListeners.has(eventType)) {
      this.notificationEventListeners.get(eventType).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in notification event listener:", error);
        }
      });
    }
  }

  /**
   * Create notification for blood request status change
   * @param {Object} params - Notification parameters
   * @param {number} params.userId - Target user ID
   * @param {number} params.requestId - Blood request ID
   * @param {string} params.status - New status
   * @param {string} params.updatedBy - Who updated the status
   * @returns {Promise<Object>} Created notification
   */
  static async createBloodRequestNotification({ userId, requestId, status, updatedBy }) {
    console.log("🩸 BloodRequestNotificationService: Creating notification with params:", {
      userId, requestId, status, updatedBy
    });

    const statusMessages = {
      0: "Yêu cầu máu của bạn đang được xem xét",
      1: "Yêu cầu máu của bạn đã được chấp nhận",
      2: "Yêu cầu máu của bạn đã hoàn thành - máu đã sẵn sàng",
      3: "Yêu cầu máu của bạn đã bị từ chối",
      4: "Yêu cầu máu của bạn đã bị hủy",
    };

    console.log("📝 Status message for status", status, ":", statusMessages[status]);

    const notificationData = {
      UserId: parseInt(userId), // API có thể mong đợi PascalCase
      Title: "Cập nhật yêu cầu máu",
      Message: statusMessages[status] || "Trạng thái yêu cầu máu đã được cập nhật",
      Type: "blood_request_update",
      RelatedId: parseInt(requestId),
      IsRead: false,
      CreatedBy: updatedBy,
      SentAt: new Date().toISOString(),
    };

    try {
      console.log("🩸 Creating blood request notification:", notificationData);

      const response = await fetch(`${config.api.baseUrl}/Notification`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("authToken")}`,
        },
        body: JSON.stringify(notificationData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log("✅ Blood request notification created:", result);

      return {
        success: true,
        data: result,
        message: "Thông báo đã được tạo thành công",
      };
    } catch (error) {
      console.error("❌ Error creating blood request notification:", error);
      return {
        success: false,
        error: error.message || "Không thể tạo thông báo",
        details: error,
      };
    }
  }
}








export default NotificationService;
